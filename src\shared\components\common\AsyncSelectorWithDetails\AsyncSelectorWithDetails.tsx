import React, { useState, useCallback, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { Loader2 } from 'lucide-react';
import { Icon, Typography } from '@/shared/components/common';

export interface AsyncSelectorOption {
  id: string | number;
  label: string;
  subtitle?: string;
  data?: Record<string, unknown>;
}

export interface AsyncSelectorWithDetailsProps<T extends AsyncSelectorOption> {
  /**
   * Giá trị đã chọn
   */
  selectedItem?: T;

  /**
   * Callback khi chọn item
   */
  onItemSelect: (item: T) => void;

  /**
   * Hàm load options từ API
   */
  loadOptions: (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => Promise<{
    items: T[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
  }>;

  /**
   * Component render chi tiết item đã chọn
   */
  renderSelectedDetails?: (item: T, onClear: () => void) => React.ReactNode;

  /**
   * Component render option trong dropdown
   */
  renderOption?: (item: T, onClick: () => void) => React.ReactNode;

  /**
   * Placeholder cho input search
   */
  placeholder?: string;

  /**
   * Label cho component
   */
  label?: string;

  /**
   * CSS class
   */
  className?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Thời gian debounce cho search (ms)
   */
  debounceTime?: number;

  /**
   * Số items mỗi trang
   */
  itemsPerPage?: number;

  /**
   * Search chỉ khi nhấn Enter
   */
  searchOnEnter?: boolean;

  /**
   * Message khi không có options
   */
  noOptionsMessage?: string;

  /**
   * Message khi đang loading
   */
  loadingMessage?: string;
}

/**
 * Component AsyncSelectorWithDetails - Selector với dropdown và hiển thị chi tiết
 * Có thể dùng cho customer, product, hoặc bất kỳ entity nào
 */
function AsyncSelectorWithDetails<T extends AsyncSelectorOption>({
  selectedItem,
  onItemSelect,
  loadOptions,
  renderSelectedDetails,
  renderOption,
  placeholder = 'Tìm kiếm...',
  label,
  className = '',
  disabled = false,
  debounceTime = 300,
  itemsPerPage = 20,
  searchOnEnter = true,
  noOptionsMessage,
  loadingMessage,
}: AsyncSelectorWithDetailsProps<T>) {
  const { t } = useTranslation(['common']);

  // States for dropdown
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [pendingSearchTerm, setPendingSearchTerm] = useState('');
  const [options, setOptions] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [dropdownPosition, setDropdownPosition] = useState<{
    top: number;
    left: number;
    width: number;
  } | null>(null);

  // Refs
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Calculate dropdown position
  const calculateDropdownPosition = useCallback(() => {
    if (!selectRef.current) return null;

    const rect = selectRef.current.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    return {
      top: rect.bottom + scrollTop + 4, // 4px gap
      left: rect.left + scrollLeft,
      width: rect.width,
    };
  }, []);

  // Load data function
  const loadData = useCallback(async (params: {
    search?: string;
    page?: number;
    reset?: boolean;
  }) => {
    const { search = '', page = 1, reset = false } = params;
    
    setLoading(true);
    try {
      const result = await loadOptions({
        search,
        page,
        limit: itemsPerPage,
      });

      if (reset || page === 1) {
        setOptions(result.items);
      } else {
        setOptions(prev => [...prev, ...result.items]);
      }

      setCurrentPage(result.currentPage);
      setHasMore(result.currentPage < result.totalPages);
    } catch (error) {
      console.error('Error loading options:', error);
      if (reset || page === 1) {
        setOptions([]);
      }
    } finally {
      setLoading(false);
    }
  }, [loadOptions, itemsPerPage]);

  // Debounced search
  const debouncedSearch = useCallback((value: string) => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      setSearchTerm(value);
      setCurrentPage(1);
      loadData({ search: value, page: 1, reset: true });
    }, debounceTime);
  }, [debounceTime, loadData]);

  // Handle search input change
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPendingSearchTerm(value);
    
    if (!searchOnEnter) {
      debouncedSearch(value);
    }
  };

  // Handle search on Enter
  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && searchOnEnter) {
      e.preventDefault();
      setSearchTerm(pendingSearchTerm);
      setCurrentPage(1);
      loadData({ search: pendingSearchTerm, page: 1, reset: true });
    }
  };

  // Load more data when scrolling
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    
    if (
      scrollHeight - scrollTop <= clientHeight + 50 && // 50px threshold
      hasMore &&
      !loading
    ) {
      loadData({ search: searchTerm, page: currentPage + 1 });
    }
  }, [hasMore, loading, searchTerm, currentPage, loadData]);

  // Handle item selection
  const handleItemSelect = useCallback((item: T) => {
    onItemSelect(item);
    setIsOpen(false);
    setPendingSearchTerm('');
  }, [onItemSelect]);

  // Handle clear selection
  const handleClearSelection = useCallback(() => {
    onItemSelect({} as T);
  }, [onItemSelect]);

  // Get display value for the select trigger
  const getDisplayValue = () => {
    if (selectedItem?.label) {
      return selectedItem.label;
    }
    return placeholder;
  };

  // Default option renderer
  const defaultRenderOption = (item: T, onClick: () => void) => (
    <div
      key={item.id}
      className="flex items-center justify-between p-2 rounded hover:bg-muted/50 cursor-pointer transition-colors"
      onClick={onClick}
    >
      <div className="flex-1">
        <Typography variant="subtitle2" className="font-medium text-foreground">
          {item.label}
        </Typography>
        {item.subtitle && (
          <Typography variant="caption" className="text-muted-foreground">
            {item.subtitle}
          </Typography>
        )}
      </div>
      <Icon name="chevron-right" size="sm" className="text-muted-foreground" />
    </div>
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const isOutsideSelect = selectRef.current && !selectRef.current.contains(target);
      const isOutsideDropdown = !document.querySelector('.async-selector-dropdown')?.contains(target);

      if (isOutsideSelect && isOutsideDropdown) {
        setIsOpen(false);
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        const position = calculateDropdownPosition();
        setDropdownPosition(position);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleScroll);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleScroll);
    };
  }, [isOpen, calculateDropdownPosition]);

  // Focus input when dropdown opens and calculate position
  useEffect(() => {
    if (isOpen) {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
      const position = calculateDropdownPosition();
      setDropdownPosition(position);
      
      // Load initial data if needed
      if (options.length === 0) {
        loadData({ search: '', page: 1, reset: true });
      }
    } else {
      setDropdownPosition(null);
    }
  }, [isOpen, calculateDropdownPosition, options.length, loadData]);

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <div className={`relative w-full ${className}`}>
      {/* Label */}
      {label && (
        <Typography variant="h6" className="mb-4">
          {label}
        </Typography>
      )}

      {/* Select trigger - chỉ hiển thị khi chưa chọn item */}
      {!selectedItem?.label && (
        <div className="relative" ref={selectRef}>
          <div
            className={`
              flex items-center justify-between px-3 py-2
              border border-input rounded-md bg-background text-foreground
              hover:bg-accent hover:text-accent-foreground
              ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
              ${isOpen ? 'ring-2 ring-ring ring-offset-2' : ''}
              w-full transition-colors
            `}
            onClick={() => !disabled && setIsOpen(!isOpen)}
          >
            <div className="flex-grow truncate text-foreground">
              {selectedItem?.label ? selectedItem.label : (
                <span className="text-muted-foreground">{getDisplayValue()}</span>
              )}
            </div>
            <div className="flex items-center">
              {loading ? (
                <Loader2 size={16} className="animate-spin text-muted-foreground" />
              ) : (
                <svg
                  className={`w-4 h-4 transition-transform text-muted-foreground ${isOpen ? 'transform rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Dropdown Portal */}
      {isOpen && dropdownPosition && createPortal(
        <div
          className="async-selector-dropdown fixed z-[99999] bg-background border border-border rounded-md shadow-lg animate-fade-in"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`,
            maxHeight: '300px',
          }}
        >
          {/* Search input */}
          <div className="sticky top-0 p-2 bg-background border-b border-border">
            <input
              ref={searchInputRef}
              type="text"
              value={pendingSearchTerm}
              onChange={handleSearchInputChange}
              onKeyDown={handleSearchKeyDown}
              placeholder={
                searchOnEnter
                  ? `${placeholder} (Nhấn Enter)`
                  : placeholder
              }
              className="w-full px-3 py-1 text-sm border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring bg-background text-foreground placeholder:text-muted-foreground"
              onClick={e => e.stopPropagation()}
            />
          </div>

          {/* Options */}
          <div
            className="max-h-60 overflow-auto custom-scrollbar auto-hide"
            onScroll={handleScroll}
          >
            {loading && options.length === 0 ? (
              <div className="px-4 py-2 text-sm text-muted-foreground flex items-center">
                <Loader2 size={14} className="animate-spin mr-2" />
                {loadingMessage || t('loading', 'Đang tải...')}
              </div>
            ) : options.length > 0 ? (
              <div className="space-y-1 p-1">
                {options.map((option) =>
                  renderOption
                    ? renderOption(option, () => handleItemSelect(option))
                    : defaultRenderOption(option, () => handleItemSelect(option))
                )}

                {/* Loading more indicator */}
                {loading && options.length > 0 && (
                  <div className="flex items-center justify-center p-2 border-t border-border">
                    <Loader2 size={14} className="animate-spin mr-2" />
                    <span className="text-xs text-muted-foreground">
                      {t('loadingMore', 'Đang tải thêm...')}
                    </span>
                  </div>
                )}
              </div>
            ) : (
              <div className="px-4 py-2 text-sm text-muted-foreground text-center">
                {noOptionsMessage || t('noResults', 'Không tìm thấy kết quả')}
              </div>
            )}
          </div>
        </div>,
        document.body
      )}

      {/* Render selected item details */}
      {selectedItem?.label && renderSelectedDetails && renderSelectedDetails(selectedItem, handleClearSelection)}
    </div>
  );
}

export default AsyncSelectorWithDetails;
