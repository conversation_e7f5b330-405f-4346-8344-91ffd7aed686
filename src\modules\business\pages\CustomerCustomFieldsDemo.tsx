import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Card } from '@/shared/components/common';
import CustomerCustomFields from '../components/forms/sections/CustomerCustomFields';
import { CustomerDetailData } from '../components/forms/sections/types';

/**
 * Demo page để test CustomerCustomFields component
 */
const CustomerCustomFieldsDemo: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);

  // Mock customer data
  const mockCustomer: CustomerDetailData = {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>ăn <PERSON>',
    email: 'nguy<PERSON><EMAIL>',
    phone: '0123456789',
    address: '123 Đường ABC, Quận 1, TP.HCM',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <Typography variant="h4" className="font-bold mb-2">
            Customer Custom Fields Demo
          </Typography>
          <Typography variant="body1" className="text-muted">
            Demo trang để test component CustomerCustomFields với SimpleCustomFieldSelector
          </Typography>
        </div>

        {/* Customer Info */}
        <Card className="p-6">
          <Typography variant="h6" className="mb-4">
            Thông tin khách hàng
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Typography variant="caption" className="text-muted">
                Tên khách hàng
              </Typography>
              <Typography variant="body2" className="font-medium">
                {mockCustomer.name}
              </Typography>
            </div>
            <div>
              <Typography variant="caption" className="text-muted">
                Email
              </Typography>
              <Typography variant="body2" className="font-medium">
                {mockCustomer.email}
              </Typography>
            </div>
            <div>
              <Typography variant="caption" className="text-muted">
                Số điện thoại
              </Typography>
              <Typography variant="body2" className="font-medium">
                {mockCustomer.phone}
              </Typography>
            </div>
            <div>
              <Typography variant="caption" className="text-muted">
                Địa chỉ
              </Typography>
              <Typography variant="body2" className="font-medium">
                {mockCustomer.address}
              </Typography>
            </div>
          </div>
        </Card>

        {/* CustomerCustomFields Component */}
        <CustomerCustomFields customer={mockCustomer} />

        {/* Instructions */}
        <Card className="p-6">
          <Typography variant="h6" className="mb-4">
            Hướng dẫn sử dụng
          </Typography>
          <div className="space-y-2">
            <Typography variant="body2">
              1. Nhấp vào ô tìm kiếm trong phần "Trường tùy chỉnh"
            </Typography>
            <Typography variant="body2">
              2. Nhập từ khóa để tìm kiếm trường tùy chỉnh
            </Typography>
            <Typography variant="body2">
              3. Chọn các trường từ dropdown để thêm vào khách hàng
            </Typography>
            <Typography variant="body2">
              4. Nhập giá trị cho các trường đã chọn
            </Typography>
            <Typography variant="body2">
              5. Sử dụng nút "X" để xóa trường không cần thiết
            </Typography>
          </div>
        </Card>

        {/* Comparison */}
        <Card className="p-6">
          <Typography variant="h6" className="mb-4">
            So sánh với ProductForm
          </Typography>
          <div className="space-y-2">
            <Typography variant="body2">
              ✅ <strong>Trước:</strong> Sử dụng Table với SlideInForm để chọn custom fields
            </Typography>
            <Typography variant="body2">
              ✅ <strong>Sau:</strong> Sử dụng SimpleCustomFieldSelector giống như ProductForm
            </Typography>
            <Typography variant="body2">
              ✅ <strong>Lợi ích:</strong> Giao diện nhất quán, dễ sử dụng hơn, ít code hơn
            </Typography>
            <Typography variant="body2">
              ✅ <strong>Tính năng:</strong> Tìm kiếm nhanh, chọn nhiều field, toggle on/off
            </Typography>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CustomerCustomFieldsDemo;
