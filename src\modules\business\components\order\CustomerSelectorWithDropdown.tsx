import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  FormItem,
  Input,
  AsyncSelectorWithDetails,
  AsyncSelectorOption,
} from '@/shared/components/common';
import { CustomerService } from '../../services/customer.service';
import { OrderCustomerDto, AddressDto } from '../../types/order.types';
import { UserConvertCustomerListItemDto } from '../../types/customer.types';

// Interface cho customer option
interface CustomerOption extends AsyncSelectorOption {
  id: string | number;
  label: string;
  subtitle: string;
  data: UserConvertCustomerListItemDto;
}

interface CustomerSelectorWithDropdownProps {
  selectedCustomer?: OrderCustomerDto;
  onCustomerSelect: (customer: OrderCustomerDto) => void;
  onCustomerChange: (customer: Partial<OrderCustomerDto>) => void;
  placeholder?: string;
  className?: string;
}

/**
 * Component chọn khách hàng với dropdown sử dụng AsyncSelectorWithDetails
 * Khi chọn sẽ hiển thị form thông tin khách hàng
 */
const CustomerSelectorWithDropdown: React.FC<CustomerSelectorWithDropdownProps> = ({
  selectedCustomer,
  onCustomerSelect,
  onCustomerChange,
  placeholder = 'Tìm kiếm khách hàng...',
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Helper function to safely extract email from customer data
  const getCustomerEmail = (email: { primary?: string } | string | null): string => {
    if (!email) return '';
    if (typeof email === 'string') return email;
    return email.primary || '';
  };

  // Load customers function for AsyncSelectorWithDetails
  const loadCustomers = useCallback(async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await CustomerService.getCustomers({
        search: params.search || '',
        page: params.page || 1,
        limit: params.limit || 20,
      });

      return {
        items: response.result.items.map((customer: UserConvertCustomerListItemDto): CustomerOption => ({
          id: customer.id,
          label: customer.name || 'N/A',
          subtitle: `${customer.phone || 'N/A'} • ${getCustomerEmail(customer.email) || 'N/A'}`,
          data: customer,
        })),
        totalItems: response.result.totalItems,
        totalPages: response.result.totalPages,
        currentPage: response.result.currentPage,
      };
    } catch (error) {
      console.error('Error loading customers:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  }, []);

  // Convert selected customer to CustomerOption
  const selectedCustomerOption: CustomerOption | undefined = selectedCustomer?.name ? {
    id: selectedCustomer.id || 0,
    label: selectedCustomer.name,
    subtitle: `${selectedCustomer.phone || 'N/A'} • ${selectedCustomer.email || 'N/A'}`,
    data: {
      id: selectedCustomer.id || 0,
      name: selectedCustomer.name,
      email: selectedCustomer.email,
      phone: selectedCustomer.phone,
    } as UserConvertCustomerListItemDto,
  } : undefined;

  // Handle customer selection
  const handleCustomerSelect = useCallback((option: CustomerOption) => {
    const customer = option.data;
    const orderCustomer: OrderCustomerDto = {
      id: customer.id,
      name: customer.name || '',
      email: getCustomerEmail(customer.email),
      phone: customer.phone || '',
      address: {
        province: '',
        district: '',
        ward: '',
        address: '',
      },
    };
    onCustomerSelect(orderCustomer);
  }, [onCustomerSelect]);

  // Xử lý thay đổi thông tin khách hàng
  const handleCustomerInfoChange = useCallback((field: string, value: string) => {
    if (field.startsWith('address.')) {
      const addressField = field.replace('address.', '');
      onCustomerChange({
        address: {
          ...selectedCustomer?.address,
          [addressField]: value,
        } as AddressDto,
      });
    } else {
      onCustomerChange({ [field]: value });
    }
  }, [selectedCustomer, onCustomerChange]);

  // Render thông tin khách hàng đã chọn
  const renderSelectedCustomerDetails = useCallback((option: CustomerOption, onClear: () => void) => {
    return (
      <Card className="mt-4 bg-card border-border">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h6" className="text-card-foreground">
              {t('business:order.customerInfo')}
            </Typography>
            <Button
              size="sm"
              variant="ghost"
              onClick={onClear}
              className="text-muted-foreground hover:text-foreground"
            >
              <Icon name="x" size="sm" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('business:customer.form.name')}>
              <Input
                value={selectedCustomer?.name || ''}
                onChange={(e) => handleCustomerInfoChange('name', e.target.value)}
                placeholder={t('business:customer.form.namePlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.phone')}>
              <Input
                value={selectedCustomer?.phone || ''}
                onChange={(e) => handleCustomerInfoChange('phone', e.target.value)}
                placeholder={t('business:customer.form.phonePlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.email')}>
              <Input
                type="email"
                value={selectedCustomer?.email || ''}
                onChange={(e) => handleCustomerInfoChange('email', e.target.value)}
                placeholder={t('business:customer.form.emailPlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.address')}>
              <Input
                value={selectedCustomer?.address?.address || ''}
                onChange={(e) => handleCustomerInfoChange('address.address', e.target.value)}
                placeholder={t('business:customer.form.addressPlaceholder')}
                fullWidth
              />
            </FormItem>
          </div>
        </div>
      </Card>
    );
  }, [selectedCustomer, handleCustomerInfoChange, t]);

  return (
    <AsyncSelectorWithDetails<CustomerOption>
      selectedItem={selectedCustomerOption}
      onItemSelect={handleCustomerSelect}
      loadOptions={loadCustomers}
      renderSelectedDetails={renderSelectedCustomerDetails}
      placeholder={placeholder}
      label={t('business:order.selectCustomer')}
      className={className}
      searchOnEnter={true}
      debounceTime={300}
      itemsPerPage={20}
      noOptionsMessage={t('business:order.noCustomersFound', 'Không tìm thấy khách hàng')}
      loadingMessage={t('common:loading', 'Đang tải...')}
    />
  );
};

export default CustomerSelectorWithDropdown;
