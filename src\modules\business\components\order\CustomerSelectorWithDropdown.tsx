import React, { useState, useCallback, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  FormItem,
  Input,
} from '@/shared/components/common';
import { useCustomerQuery } from '../../hooks/useCustomerQuery';
import { OrderCustomerDto, AddressDto } from '../../types/order.types';
import { UserConvertCustomerListItemDto } from '../../types/customer.types';
import { debounce } from 'lodash';

interface CustomerSelectorWithDropdownProps {
  selectedCustomer?: OrderCustomerDto;
  onCustomerSelect: (customer: OrderCustomerDto) => void;
  onCustomerChange: (customer: Partial<OrderCustomerDto>) => void;
  placeholder?: string;
  className?: string;
}

/**
 * Component chọn khách hàng với dropdown giống AsyncSelectWithPagination
 * <PERSON><PERSON> chọn sẽ hiển thị form thông tin khách hàng
 */
const CustomerSelectorWithDropdown: React.FC<CustomerSelectorWithDropdownProps> = ({
  selectedCustomer,
  onCustomerSelect,
  onCustomerChange,
  placeholder = 'Tìm kiếm khách hàng...',
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);
  
  // States for dropdown
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [pendingSearchTerm, setPendingSearchTerm] = useState('');
  const [dropdownPosition, setDropdownPosition] = useState<{
    top: number;
    left: number;
    width: number;
  } | null>(null);

  // Refs
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Hook lấy danh sách khách hàng
  const { data: customersData, isLoading } = useCustomerQuery({
    search: searchTerm,
    limit: 20,
  });

  // Debounced search
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchTerm(value);
    }, 300),
    []
  );

  // Helper function to safely extract email from customer data
  const getCustomerEmail = (email: { primary?: string } | string | null): string => {
    if (!email) return '';
    if (typeof email === 'string') return email;
    return email.primary || '';
  };

  // Calculate dropdown position
  const calculateDropdownPosition = useCallback(() => {
    if (!selectRef.current) return null;

    const rect = selectRef.current.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    return {
      top: rect.bottom + scrollTop + 4, // 4px gap
      left: rect.left + scrollLeft,
      width: rect.width,
    };
  }, []);

  // Handle search input change
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPendingSearchTerm(value);
    debouncedSearch(value);
  };

  // Handle search on Enter
  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      setSearchTerm(pendingSearchTerm);
    }
  };

  // Xử lý chọn khách hàng từ dropdown
  const handleSelectCustomer = useCallback((customer: UserConvertCustomerListItemDto) => {
    const orderCustomer: OrderCustomerDto = {
      id: customer.id,
      name: customer.name || '',
      email: getCustomerEmail(customer.email),
      phone: customer.phone || '',
      address: {
        province: '',
        district: '',
        ward: '',
        address: '',
      },
    };
    onCustomerSelect(orderCustomer);
    setIsOpen(false);
    setPendingSearchTerm('');
  }, [onCustomerSelect]);

  // Xử lý thay đổi thông tin khách hàng
  const handleCustomerInfoChange = useCallback((field: string, value: string) => {
    if (field.startsWith('address.')) {
      const addressField = field.replace('address.', '');
      onCustomerChange({
        address: {
          ...selectedCustomer?.address,
          [addressField]: value,
        } as AddressDto,
      });
    } else {
      onCustomerChange({ [field]: value });
    }
  }, [selectedCustomer, onCustomerChange]);

  // Get display value for the select trigger
  const getDisplayValue = () => {
    if (selectedCustomer?.name) {
      return selectedCustomer.name;
    }
    return placeholder;
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const isOutsideSelect = selectRef.current && !selectRef.current.contains(target);
      const isOutsideDropdown = !document.querySelector('.customer-selector-dropdown')?.contains(target);

      if (isOutsideSelect && isOutsideDropdown) {
        setIsOpen(false);
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        const position = calculateDropdownPosition();
        setDropdownPosition(position);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleScroll);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleScroll);
    };
  }, [isOpen, calculateDropdownPosition]);

  // Focus input when dropdown opens and calculate position
  useEffect(() => {
    if (isOpen) {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
      const position = calculateDropdownPosition();
      setDropdownPosition(position);
    } else {
      setDropdownPosition(null);
    }
  }, [isOpen, calculateDropdownPosition]);

  // Render thông tin khách hàng đã chọn
  const renderSelectedCustomer = () => {
    if (!selectedCustomer?.name) return null;

    return (
      <Card className="mt-4">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h6">
              {t('business:order.customerInfo')}
            </Typography>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onCustomerSelect({} as OrderCustomerDto)}
            >
              <Icon name="x" size="sm" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('business:customer.form.name')}>
              <Input
                value={selectedCustomer.name}
                onChange={(e) => handleCustomerInfoChange('name', e.target.value)}
                placeholder={t('business:customer.form.namePlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.phone')}>
              <Input
                value={selectedCustomer.phone}
                onChange={(e) => handleCustomerInfoChange('phone', e.target.value)}
                placeholder={t('business:customer.form.phonePlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.email')}>
              <Input
                type="email"
                value={selectedCustomer.email}
                onChange={(e) => handleCustomerInfoChange('email', e.target.value)}
                placeholder={t('business:customer.form.emailPlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.address')}>
              <Input
                value={selectedCustomer.address?.address || ''}
                onChange={(e) => handleCustomerInfoChange('address.address', e.target.value)}
                placeholder={t('business:customer.form.addressPlaceholder')}
                fullWidth
              />
            </FormItem>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className={`relative w-full ${className}`}>
      <Typography variant="h6" className="mb-4">
        {t('business:order.selectCustomer')}
      </Typography>

      {/* Select trigger - chỉ hiển thị khi chưa chọn khách hàng */}
      {!selectedCustomer?.name && (
        <div className="relative" ref={selectRef}>
          <div
            className={`
              flex items-center justify-between px-3 py-2
              border-0 rounded-md bg-card-muted text-foreground
              cursor-pointer
              ${isOpen ? 'ring-2 ring-primary/30' : ''}
              w-full
            `}
            onClick={() => setIsOpen(!isOpen)}
          >
            <div className="flex-grow truncate">{getDisplayValue()}</div>
            <div className="flex items-center">
              {isLoading ? (
                <Icon name="loading" className="animate-spin" size="sm" />
              ) : (
                <svg
                  className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Dropdown Portal */}
      {isOpen && dropdownPosition && createPortal(
        <div
          className="customer-selector-dropdown fixed z-[99999] bg-card rounded-md shadow-lg border-0 animate-fade-in"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`,
            maxHeight: '300px',
          }}
        >
          {/* Search input */}
          <div className="sticky top-0 p-2 bg-card border-b-0">
            <input
              ref={searchInputRef}
              type="text"
              value={pendingSearchTerm}
              onChange={handleSearchInputChange}
              onKeyDown={handleSearchKeyDown}
              placeholder={t('business:order.searchCustomerPlaceholder', 'Tìm kiếm khách hàng... (Nhấn Enter)')}
              className="w-full px-3 py-1 text-sm border-0 rounded-md focus:outline-none focus:ring-1 focus:ring-primary/20 bg-card-muted text-foreground"
              onClick={e => e.stopPropagation()}
            />
          </div>

          {/* Customer options */}
          <div className="max-h-60 overflow-auto custom-scrollbar auto-hide">
            {isLoading ? (
              <div className="px-4 py-2 text-sm text-muted flex items-center">
                <Icon name="loading" className="animate-spin mr-2" size="sm" />
                {t('common:loading', 'Đang tải...')}
              </div>
            ) : customersData?.items?.length ? (
              <div className="space-y-1 p-1">
                {customersData.items.map((customer: UserConvertCustomerListItemDto) => (
                  <div
                    key={customer.id}
                    className="flex items-center justify-between p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                    onClick={() => handleSelectCustomer(customer)}
                  >
                    <div className="flex-1">
                      <Typography variant="subtitle2" className="font-medium">
                        {customer.name || 'N/A'}
                      </Typography>
                      <Typography variant="caption" className="text-muted">
                        {customer.phone || 'N/A'} • {getCustomerEmail(customer.email) || 'N/A'}
                      </Typography>
                    </div>
                    <Icon name="chevron-right" size="sm" className="text-muted" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="px-4 py-2 text-sm text-muted text-center">
                {t('business:order.noCustomersFound', 'Không tìm thấy khách hàng')}
              </div>
            )}
          </div>
        </div>,
        document.body
      )}

      {/* Render selected customer info */}
      {selectedCustomer?.name && renderSelectedCustomer()}
    </div>
  );
};

export default CustomerSelectorWithDropdown;
